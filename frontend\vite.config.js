import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from 'path';

export default defineConfig(({ mode }) => {
  /* eslint-disable no-undef */
  const env = loadEnv(mode, process.cwd());
  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    define: {
      "import.meta.env.VITE_ENV": JSON.stringify(env.VITE_ENV),
      "import.meta.env.VITE_API_URL": JSON.stringify(env.VITE_API_URL),
      "import.meta.env.VITE_LOGIN_URL": JSON.stringify(env.VITE_LOGIN_URL),
      "import.meta.env.VITE_FORGET_URL": JSON.stringify(env.VITE_FORGET_URL),
      "import.meta.env.VITE_API_URL_AUTH": JSON.stringify(env.VITE_API_URL_AUTH),
      "import.meta.env.VITE_API_URL_USERS": JSON.stringify(env.VITE_API_URL_USERS),
      "import.meta.env.VITE_API_URL_STUDENTS": JSON.stringify(env.VITE_API_URL_STUDENTS),
      "import.meta.env.VITE_API_URL_COURSES": JSON.stringify(env.VITE_API_URL_COURSES),
      "import.meta.env.VITE_API_URL_PARENTS": JSON.stringify(env.VITE_API_URL_PARENTS),
    },
    server: {
      host: "0.0.0.0",
      port: 5173,
      allowedHosts: ["pandra2"], // Add allowed hosts
    },
  };
});