"""
Views for the Auth Service.

This module defines the API endpoints for the Auth Service.

English: This file defines the API endpoints for the Auth Service
Tanglish: Indha file Auth Service-kku API endpoints-a define pannum
"""

from flask import Blueprint, jsonify, request
from auth_service.controllers import login, verify_token, forget_password, verify_otp, reset_password

# Create a Blueprint for auth routes
auth_bp = Blueprint('auth', __name__)

# Add route handlers for OPTIONS requests
@auth_bp.route('/<path:path>', methods=['OPTIONS'])
def handle_options_path(path):
    """
    Handle OPTIONS requests for CORS preflight with path.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a handle pannum
    """
    return "", 200

@auth_bp.route('/', methods=['OPTIONS'])
def handle_options_root():
    """
    Handle OPTIONS requests for CORS preflight at root.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS at root
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a root-la handle pannum
    """
    return "", 200

@auth_bp.route('/login', methods=['POST', 'OPTIONS'])
def login_route():
    """
    Login endpoint.

    Returns:
        Response from login controller

    English: This endpoint handles user login
    Tanglish: Indha endpoint user login-a handle pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200

    print("Login route accessed")
    return login()

@auth_bp.route('/verify', methods=['GET', 'OPTIONS'])
def verify_route():
    """
    Token verification endpoint.

    Returns:
        Response from verify_token controller

    English: This endpoint checks if a token is valid
    Tanglish: Indha endpoint token valid-a irukka nu check pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200

    return verify_token()

@auth_bp.route('/health', methods=['GET', 'OPTIONS'])
def health_check():
    """
    Health check endpoint.

    Returns:
        JSON response indicating the service is running

    English: This endpoint checks if the service is running
    Tanglish: Indha endpoint service odi kondu irukka nu check pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200

    return jsonify({"status": "healthy", "service": "auth"})

@auth_bp.route('/forget-password', methods=['POST', 'OPTIONS'])
def forget_password_route():
    """
    Forget password endpoint.

    Returns:
        Response from forget_password controller

    English: This endpoint handles password reset request
    Tanglish: Indha endpoint password reset request-a handle pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200

    print("Forget password route accessed")
    return forget_password()

@auth_bp.route('/verify-otp', methods=['POST', 'OPTIONS'])
def verify_otp_route():
    """
    Verify OTP endpoint.

    Returns:
        Response from verify_otp controller

    English: This endpoint verifies OTP for password reset
    Tanglish: Indha endpoint password reset-kku OTP verify pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200

    print("Verify OTP route accessed")
    return verify_otp()

@auth_bp.route('/reset-password', methods=['POST', 'OPTIONS'])
def reset_password_route():
    """
    Reset password endpoint.

    Returns:
        Response from reset_password controller

    English: This endpoint resets user password
    Tanglish: Indha endpoint user password reset pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200

    print("Reset password route accessed")
    return reset_password()
