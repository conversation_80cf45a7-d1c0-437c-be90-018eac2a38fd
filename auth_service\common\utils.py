"""
Utility functions shared across microservices.

This module provides common utility functions that are used by multiple microservices.

English: This file contains helper functions used by all services
Tanglish: Indha file-la ella services-um use panna helper functions irukku
"""

import json
import logging
import random
import string
import requests
from functools import wraps
from flask import jsonify, request

# Configure logging
logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def handle_error(error_message, status_code=400):
    """
    Create a standardized error response.

    Args:
        error_message: The error message to return
        status_code: HTTP status code (default: 400)

    Returns:
        JSON response with error message and status code

    English: This function creates a standard error response with message and status code
    Tanglish: Indha function standard error response-a create pannum, message-um status code-um set pannum
    """
    logger.error(f"Error: {error_message}, Status: {status_code}")
    response = jsonify({"error": error_message})
    response.status_code = status_code

    # We don't need to add CORS headers here as Flask-CORS is handling it
    # response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
    # response.headers.add('Access-Control-Allow-Credentials', 'true')
    # response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    # response.headers.add('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')

    return response

def validate_request_data(required_fields):
    """
    Decorator to validate that required fields are present in the request data.

    Args:
        required_fields: List of field names that must be present

    Returns:
        Decorator function

    English: This decorator checks if all required fields are in the request
    Tanglish: Indha decorator request-la ella required fields-um irukka nu check pannum
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            data = request.get_json()
            if not data:
                return handle_error("No data provided", 400)

            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def call_service(service_url, method='GET', data=None, headers=None):
    """
    Make an HTTP request to another microservice.

    Args:
        service_url: URL of the service to call
        method: HTTP method (GET, POST, etc.)
        data: Data to send in the request
        headers: Headers to include in the request

    Returns:
        Response from the service

    English: This function calls another microservice using HTTP
    Tanglish: Indha function vera oru microservice-a HTTP moolama call pannum
    """
    default_headers = {'Content-Type': 'application/json'}
    if headers:
        default_headers.update(headers)

    try:
        if method.upper() == 'GET':
            response = requests.get(service_url, headers=default_headers)
        elif method.upper() == 'POST':
            response = requests.post(service_url, json=data, headers=default_headers)
        elif method.upper() == 'PUT':
            response = requests.put(service_url, json=data, headers=default_headers)
        elif method.upper() == 'DELETE':
            response = requests.delete(service_url, headers=default_headers)
        else:
            logger.error(f"Unsupported HTTP method: {method}")
            return None

        return response

    except requests.exceptions.RequestException as e:
        logger.error(f"Error calling service {service_url}: {str(e)}")
        return None


def generate_school_code():
    """
    Generate a unique school code in the format of 6 characters (3 letters followed by 3 numbers).

    Returns:
        A string containing 3 uppercase letters followed by 3 numbers

    English: This function generates a unique school code for school-wise separation
    Tanglish: Indha function school-wise separation-kku unique school code-a generate pannum
    """
    # Generate 3 random uppercase letters
    letters = ''.join(random.choices(string.ascii_uppercase, k=3))

    # Generate 3 random numbers
    numbers = ''.join(random.choices(string.digits, k=3))

    # Combine letters and numbers to form the school code
    school_code = f"{letters}{numbers}"

    return school_code


def send_otp_email(email, otp, username):
    """
    Send OTP email for password reset.

    Args:
        email: Recipient email address
        otp: OTP code to send
        username: Username of the user

    Returns:
        True if email sent successfully, False otherwise

    English: This function sends OTP email for password reset
    Tanglish: Indha function password reset-kku OTP email send pannum
    """
    try:
        from flask_mail import Mail, Message
        from flask import current_app

        # Get mail instance from current app
        mail = Mail(current_app)

        # Create email message
        subject = "Password Reset OTP - School Management System"

        # HTML email template
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }}
                .otp-box {{ background-color: #4f46e5; color: white; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 3px; border-radius: 8px; margin: 20px 0; }}
                .warning {{ background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Password Reset Request</h1>
                </div>
                <div class="content">
                    <h2>Hello {username},</h2>
                    <p>We received a request to reset your password for your School Management System account.</p>

                    <p>Your One-Time Password (OTP) is:</p>
                    <div class="otp-box">{otp}</div>

                    <div class="warning">
                        <strong>Important:</strong> This OTP is valid for 15 minutes only. Do not share this code with anyone.
                    </div>

                    <p>If you didn't request this password reset, please ignore this email or contact support if you have concerns.</p>

                    <p>Best regards,<br>School Management System Team</p>
                </div>
                <div class="footer">
                    <p>This is an automated message. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Plain text version
        text_body = f"""
        Password Reset Request

        Hello {username},

        We received a request to reset your password for your School Management System account.

        Your One-Time Password (OTP) is: {otp}

        Important: This OTP is valid for 15 minutes only. Do not share this code with anyone.

        If you didn't request this password reset, please ignore this email or contact support if you have concerns.

        Best regards,
        School Management System Team

        This is an automated message. Please do not reply to this email.
        """

        # Create message
        msg = Message(
            subject=subject,
            recipients=[email],
            html=html_body,
            body=text_body
        )

        # Send email
        mail.send(msg)
        logger.info(f"OTP email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send OTP email to {email}: {str(e)}")
        return False


def send_password_reset_confirmation_email(email, username):
    """
    Send password reset confirmation email.

    Args:
        email: Recipient email address
        username: Username of the user

    Returns:
        True if email sent successfully, False otherwise

    English: This function sends password reset confirmation email
    Tanglish: Indha function password reset confirmation email send pannum
    """
    try:
        from flask_mail import Mail, Message
        from flask import current_app

        # Get mail instance from current app
        mail = Mail(current_app)

        # Create email message
        subject = "Password Reset Successful - School Management System"

        # HTML email template
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }}
                .success-box {{ background-color: #d1fae5; border-left: 4px solid #10b981; padding: 15px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Password Reset Successful</h1>
                </div>
                <div class="content">
                    <h2>Hello {username},</h2>

                    <div class="success-box">
                        <strong>Success!</strong> Your password has been reset successfully.
                    </div>

                    <p>Your password for the School Management System has been changed successfully. You can now log in with your new password.</p>

                    <p>If you didn't make this change, please contact support immediately.</p>

                    <p>Best regards,<br>School Management System Team</p>
                </div>
                <div class="footer">
                    <p>This is an automated message. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Plain text version
        text_body = f"""
        Password Reset Successful

        Hello {username},

        Your password for the School Management System has been changed successfully. You can now log in with your new password.

        If you didn't make this change, please contact support immediately.

        Best regards,
        School Management System Team

        This is an automated message. Please do not reply to this email.
        """

        # Create message
        msg = Message(
            subject=subject,
            recipients=[email],
            html=html_body,
            body=text_body
        )

        # Send email
        mail.send(msg)
        logger.info(f"Password reset confirmation email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send password reset confirmation email to {email}: {str(e)}")
        return False
