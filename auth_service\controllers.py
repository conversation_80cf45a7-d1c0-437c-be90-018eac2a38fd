"""
Controllers for the Auth Service.

This module contains the business logic for authentication.

English: This file contains the logic for user login and token generation
Tanglish: Indha file-la user login and token generation-kku logic irukku
"""

import os
import jwt
from datetime import datetime, timedelta
from flask import jsonify, request, current_app
from auth_service.common.utils import handle_error, send_otp_email, send_password_reset_confirmation_email
from auth_service.models import User
from auth_service.common.db_config import db
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

def login():
    """
    Authenticate a user and generate a JWT token.

    Returns:
        JSON response with token and user information

    English: This function checks username and password, then generates a JWT token
    Tanglish: Indha function username, password-a check panni, JWT token-a generate pannum
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if username and password are provided
    username = data.get('username')
    password = data.get('password')
    main_code = data.get('main_code')  # Get main_code if provided

    if not username or not password:
        return handle_error("Username and password are required", 400)

    print(f"Login attempt for username: {username}")

    # Find the user
    user = User.query.filter_by(username=username).first()
    if not user:
        print(f"User not found: {username}")
        return handle_error("Invalid username or password", 401)

    # For Teacher and Admin roles, check if main_code is provided and store it
    if user.role in ['Teacher', 'Admin'] and main_code:
        user.main_code = main_code
        db.session.commit()
        print(f"Updated main_code for {username} to {main_code}")

    print(f"User found: {user.username}, ID: {user.id}, Role: {user.role}")

    # Check password
    password_match = user.check_password(password)
    print(f"Password match: {password_match}")

    if not password_match:
        print(f"Password verification failed for user: {username}")
        return handle_error("Invalid username or password", 401)

    print(f"Password verified successfully for user: {username}")

    # Generate JWT token
    token = generate_token(user)

    # Create response with token and user information
    response = jsonify({
        "token": token,
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "is_admin": user.is_admin,
            "course": user.course,
            "main_code": user.main_code
        }
    })

    # We don't need to add CORS headers here as Flask-CORS is handling it
    # response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
    # response.headers.add('Access-Control-Allow-Credentials', 'true')
    # response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    # response.headers.add('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')

    return response

def generate_token(user):
    """
    Generate a JWT token for a user.

    Args:
        user: User object

    Returns:
        JWT token string

    English: This function creates a JWT token with user information
    Tanglish: Indha function user information-oda JWT token-a create pannum
    """
    # Set token expiration time (e.g., 24 hours)
    expiration = datetime.utcnow() + timedelta(hours=24)

    # Create token payload
    payload = {
        'sub': str(user.id),  # Convert user ID to string
        'username': user.username,
        'role': user.role,
        'is_admin': user.is_admin,
        'exp': expiration
    }

    # Generate token
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')

    return token

def verify_token():
    """
    Verify a JWT token.

    Returns:
        JSON response with token validity and user information

    English: This function checks if a token is valid
    Tanglish: Indha function token valid-a irukka nu check pannum
    """
    # Get token from Authorization header
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return handle_error("Missing or invalid token", 401)

    token = auth_header.split(' ')[1]

    try:
        # Decode and verify token
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])

        # Get user from database
        user_id = payload.get('sub')
        user = User.query.get(user_id)
        if not user:
            return handle_error("User not found", 404)

        # Create response with user information
        response = jsonify({
            "valid": True,
            "user": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "role": user.role,
                "is_admin": user.is_admin,
                "course": user.course,
                "main_code": user.main_code
            }
        })

        # We don't need to add CORS headers here as Flask-CORS is handling it
        # response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
        # response.headers.add('Access-Control-Allow-Credentials', 'true')
        # response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        # response.headers.add('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')

        return response

    except jwt.ExpiredSignatureError:
        return handle_error("Token expired", 401)

    except jwt.InvalidTokenError:
        return handle_error("Invalid token", 401)


def forget_password():
    """
    Initiate password reset by sending OTP to user's email.

    Returns:
        JSON response indicating success or failure

    English: This function sends OTP to user's email for password reset
    Tanglish: Indha function password reset-kku user email-kku OTP send pannum
    """
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return handle_error("No data provided", 400)

        email = data.get('email')
        if not email:
            return handle_error("Email is required", 400)

        print(f"Password reset request for email: {email}")

        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            # For security, don't reveal if email exists or not
            return jsonify({"message": "If the email exists, an OTP has been sent"}), 200

        print(f"User found: {user.username}, ID: {user.id}")

        # Generate and save OTP
        otp = user.generate_reset_otp()
        db.session.commit()

        print(f"Generated OTP: {otp} for user: {user.username}")

        # Send OTP email
        email_sent = send_otp_email(user.email, otp, user.username)

        if email_sent:
            print(f"OTP email sent successfully to {user.email}")
            return jsonify({"message": "If the email exists, an OTP has been sent"}), 200
        else:
            print(f"Failed to send OTP email to {user.email}")
            # Clear OTP if email failed
            user.clear_reset_otp()
            db.session.commit()
            return handle_error("Failed to send OTP email", 500)

    except Exception as e:
        print(f"Error in forget_password: {str(e)}")
        return handle_error(f"An error occurred: {str(e)}", 500)


def verify_otp():
    """
    Verify OTP for password reset.

    Returns:
        JSON response indicating OTP validity

    English: This function verifies the OTP for password reset
    Tanglish: Indha function password reset-kku OTP verify pannum
    """
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return handle_error("No data provided", 400)

        email = data.get('email')
        otp = data.get('otp')

        if not email or not otp:
            return handle_error("Email and OTP are required", 400)

        print(f"OTP verification request for email: {email}, OTP: {otp}")

        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            return handle_error("Invalid email or OTP", 400)

        # Verify OTP
        if user.verify_reset_otp(otp):
            print(f"OTP verified successfully for user: {user.username}")
            return jsonify({"message": "OTP verified successfully", "valid": True}), 200
        else:
            print(f"Invalid or expired OTP for user: {user.username}")
            return handle_error("Invalid or expired OTP", 400)

    except Exception as e:
        print(f"Error in verify_otp: {str(e)}")
        return handle_error(f"An error occurred: {str(e)}", 500)


def reset_password():
    """
    Reset user password after OTP verification.

    Returns:
        JSON response indicating success or failure

    English: This function resets user password after OTP verification
    Tanglish: Indha function OTP verify panna apram user password reset pannum
    """
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return handle_error("No data provided", 400)

        email = data.get('email')
        otp = data.get('otp')
        new_password = data.get('new_password')

        if not email or not otp or not new_password:
            return handle_error("Email, OTP, and new password are required", 400)

        # Validate password strength
        if len(new_password) < 6:
            return handle_error("Password must be at least 6 characters long", 400)

        print(f"Password reset request for email: {email}")

        # Find user by email
        user = User.query.filter_by(email=email).first()
        if not user:
            return handle_error("Invalid email or OTP", 400)

        # Verify OTP one more time
        if not user.verify_reset_otp(otp):
            print(f"Invalid or expired OTP for user: {user.username}")
            return handle_error("Invalid or expired OTP", 400)

        # Update password
        user.update_password(new_password)

        # Clear OTP
        user.clear_reset_otp()

        # Save changes
        db.session.commit()

        print(f"Password reset successfully for user: {user.username}")

        # Send confirmation email
        send_password_reset_confirmation_email(user.email, user.username)

        return jsonify({"message": "Password reset successfully"}), 200

    except Exception as e:
        print(f"Error in reset_password: {str(e)}")
        return handle_error(f"An error occurred: {str(e)}", 500)
