/**
 * Forget Password Component
 *
 * This component handles the password reset flow with OTP verification.
 *
 * English: This component handles password reset with email OTP
 * Tanglish: Indha component email OTP-oda password reset-a handle pannum
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import authService from '@/services/authService';
import Button from '@/components/field/Button';
import Input from '@/components/field/Input';

const ForgetPassword = () => {
  const [step, setStep] = useState(1); // 1: Email, 2: OTP, 3: New Password
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const navigate = useNavigate();

  /**
   * Handle email submission for OTP request
   *
   * English: This function sends OTP to the provided email
   * Tanglish: Indha function kudukkapatta email-kku OTP send pannum
   */
  const handleEmailSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      setError('Email is required');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      await authService.forgetPassword(email);
      setSuccess('OTP has been sent to your email address');
      setStep(2);
    } catch (error) {
      setError(error.error || 'Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle OTP verification
   *
   * English: This function verifies the OTP entered by user
   * Tanglish: Indha function user enter panna OTP-a verify pannum
   */
  const handleOTPSubmit = async (e) => {
    e.preventDefault();

    if (!otp) {
      setError('OTP is required');
      return;
    }

    if (otp.length !== 6) {
      setError('OTP must be 6 digits');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      await authService.verifyOTP(email, otp);
      setSuccess('OTP verified successfully');
      setStep(3);
    } catch (error) {
      setError(error.error || 'Invalid or expired OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle password reset
   *
   * English: This function resets the password with new password
   * Tanglish: Indha function puthusa password-oda password reset pannum
   */
  const handlePasswordReset = async (e) => {
    e.preventDefault();

    if (!newPassword || !confirmPassword) {
      setError('Both password fields are required');
      return;
    }

    if (newPassword.length < 6) {
      setError('Password must be at least 6 characters long');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      await authService.resetPassword(email, otp, newPassword);
      setSuccess('Password reset successfully! You can now login with your new password.');
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      setError(error.error || 'Failed to reset password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Go back to previous step
   *
   * English: This function goes back to the previous step
   * Tanglish: Indha function previous step-kku ponum
   */
  const goBack = () => {
    setError('');
    setSuccess('');
    if (step > 1) {
      setStep(step - 1);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-purple-100">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-xl shadow-lg border-t-4 border-indigo-500 transform transition-all hover:scale-105 duration-300">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-indigo-700">Reset Password</h1>
          <p className="mt-2 text-gray-600">
            {step === 1 && "Enter your email to receive OTP"}
            {step === 2 && "Enter the OTP sent to your email"}
            {step === 3 && "Enter your new password"}
          </p>
        </div>

        {error && (
          <div className="p-4 mb-4 rounded-md bg-red-100 text-red-700" role="alert">
            {error}
          </div>
        )}

        {success && (
          <div className="p-4 mb-4 rounded-md bg-green-100 text-green-700" role="alert">
            {success}
          </div>
        )}

        {/* Step 1: Email Input */}
        {step === 1 && (
          <form onSubmit={handleEmailSubmit} className="space-y-6">
            <Input
              label="Email Address"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address"
              required
            />

            <Button
              type="submit"
              loading={loading}
              className="w-full"
            >
              Send OTP
            </Button>

            <div className="text-center">
              <button
                type="button"
                onClick={() => navigate('/login')}
                className="text-indigo-600 hover:text-indigo-500 text-sm"
              >
                Back to Login
              </button>
            </div>
          </form>
        )}

        {/* Step 2: OTP Input */}
        {step === 2 && (
          <form onSubmit={handleOTPSubmit} className="space-y-6">
            <Input
              label="Enter OTP"
              type="text"
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="Enter 6-digit OTP"
              maxLength={6}
              required
            />

            <div className="text-sm text-gray-600 text-center">
              OTP sent to: <span className="font-medium">{email}</span>
            </div>

            <Button
              type="submit"
              loading={loading}
              className="w-full"
            >
              Verify OTP
            </Button>

            <div className="text-center space-x-4">
              <button
                type="button"
                onClick={goBack}
                className="text-gray-600 hover:text-gray-500 text-sm"
              >
                Change Email
              </button>
              <button
                type="button"
                onClick={handleEmailSubmit}
                disabled={loading}
                className="text-indigo-600 hover:text-indigo-500 text-sm"
              >
                Resend OTP
              </button>
            </div>
          </form>
        )}

        {/* Step 3: New Password Input */}
        {step === 3 && (
          <form onSubmit={handlePasswordReset} className="space-y-6">
            <Input
              label="New Password"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="Enter new password"
              required
            />

            <Input
              label="Confirm New Password"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm new password"
              required
            />

            <div className="text-sm text-gray-600">
              Password must be at least 6 characters long
            </div>

            <Button
              type="submit"
              loading={loading}
              className="w-full"
            >
              Reset Password
            </Button>

            <div className="text-center">
              <button
                type="button"
                onClick={goBack}
                className="text-gray-600 hover:text-gray-500 text-sm"
              >
                Back to OTP
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ForgetPassword;
