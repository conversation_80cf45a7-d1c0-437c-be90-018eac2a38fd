"""
Test Script for Password Reset Functionality

This script tests the forget password, verify OTP, and reset password endpoints.

English: This script tests the password reset functionality
Tanglish: Indha script password reset functionality-a test pannum
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:5000/api/auth"

def test_forget_password(email):
    """Test the forget password endpoint."""
    print(f"\n=== Testing Forget Password for {email} ===")
    
    url = f"{BASE_URL}/forget-password"
    data = {"email": email}
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_verify_otp(email, otp):
    """Test the verify OTP endpoint."""
    print(f"\n=== Testing Verify OTP for {email} with OTP: {otp} ===")
    
    url = f"{BASE_URL}/verify-otp"
    data = {"email": email, "otp": otp}
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_reset_password(email, otp, new_password):
    """Test the reset password endpoint."""
    print(f"\n=== Testing Reset Password for {email} ===")
    
    url = f"{BASE_URL}/reset-password"
    data = {"email": email, "otp": otp, "new_password": new_password}
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_login(username, password):
    """Test login with new password."""
    print(f"\n=== Testing Login with new password ===")
    
    url = f"{BASE_URL}/login"
    data = {"username": username, "password": password}
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Main test function."""
    print("=== Password Reset API Test ===")
    print("Make sure the auth service is running on port 5000")
    
    # Test data - replace with actual user email
    test_email = "<EMAIL>"  # Replace with a real email from your database
    test_username = "testuser"       # Replace with actual username
    new_password = "newpassword123"
    
    print(f"\nTesting with email: {test_email}")
    print("Note: Replace test_email with a real email from your database")
    
    # Step 1: Test forget password
    if test_forget_password(test_email):
        print("✓ Forget password request sent successfully")
        
        # In a real scenario, you would check your email for the OTP
        # For testing, you can check the console output of the auth service
        otp = input("\nEnter the OTP from the email (or check auth service console): ")
        
        # Step 2: Test verify OTP
        if test_verify_otp(test_email, otp):
            print("✓ OTP verified successfully")
            
            # Step 3: Test reset password
            if test_reset_password(test_email, otp, new_password):
                print("✓ Password reset successfully")
                
                # Step 4: Test login with new password
                if test_login(test_username, new_password):
                    print("✓ Login with new password successful")
                    print("\n🎉 All tests passed!")
                else:
                    print("❌ Login with new password failed")
            else:
                print("❌ Password reset failed")
        else:
            print("❌ OTP verification failed")
    else:
        print("❌ Forget password request failed")

if __name__ == "__main__":
    main()
