"""
Models for the Auth Service.

This module defines the database models for the Auth Service.

English: This file defines the database tables for the Auth Service
Tanglish: Indha file Auth Service-kku database tables-a define pannum
"""

from datetime import datetime
import bcrypt
from auth_service.common.db_config import db
from auth_service.common.utils import generate_school_code

class User(db.Model):
    """
    User model for authentication.

    English: This model stores user information for login and authentication
    Tanglish: Indha model user information-a login and authentication-kku store pannum
    """
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    role = db.Column(db.String(20), nullable=False)  # Super Admin, Admin, Teacher, Student, Parent
    is_admin = db.Column(db.<PERSON>, default=False)  # For Teachers who are also Admins
    course = db.Column(db.String(50), nullable=True)  # For Teachers (Neet, Jee, etc.)
    main_code = db.Column(db.String(50), nullable=True)  # For school-wise separation

    # OTP fields for password reset
    reset_otp = db.Column(db.String(6), nullable=True)  # 6-digit OTP
    reset_otp_expires = db.Column(db.DateTime, nullable=True)  # OTP expiration time

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, username, password, email, role, is_admin=False, course=None, main_code=None):
        """
        Initialize a new User.

        Args:
            username: User's username
            password: User's password (will be hashed)
            email: User's email
            role: User's role (Super Admin, Admin, Teacher, Student, Parent)
            is_admin: Whether the user is an admin (default: False)
            course: Course for Teacher (Neet, Jee, etc.) (default: None)
            main_code: School code for school-wise separation (default: None)

        English: This function creates a new user with the given information
        Tanglish: Indha function kudukkapatta information-oda puthusa oru user-a create pannum
        """
        self.username = username
        self.password = self._hash_password(password)
        self.email = email
        self.role = role
        self.is_admin = is_admin
        self.course = course

        # Generate a unique school code for Super Admin users if not provided
        if role == 'Super Admin' and main_code is None:
            self.main_code = generate_school_code()
        else:
            self.main_code = main_code

    def _hash_password(self, password):
        """
        Hash a password using bcrypt.

        Args:
            password: Plain text password

        Returns:
            Hashed password

        English: This function securely hashes the password
        Tanglish: Indha function password-a secure-a hash pannum
        """
        # Convert password to bytes if it's a string
        if isinstance(password, str):
            password = password.encode('utf-8')

        # Generate a salt and hash the password
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password, salt)

        # Return the hashed password as a string
        return hashed.decode('utf-8')

    def check_password(self, password):
        """
        Check if a password matches the stored hash.

        Args:
            password: Plain text password to check

        Returns:
            True if the password matches, False otherwise

        English: This function checks if the password is correct
        Tanglish: Indha function password correct-a irukka nu check pannum
        """
        # Convert password to bytes if it's a string
        if isinstance(password, str):
            password = password.encode('utf-8')

        # Convert stored hash to bytes if it's a string
        stored_password = self.password
        if isinstance(stored_password, str):
            stored_password = stored_password.encode('utf-8')

        # Check if the password matches
        return bcrypt.checkpw(password, stored_password)

    def generate_reset_otp(self):
        """
        Generate a 6-digit OTP for password reset.

        Returns:
            Generated OTP string

        English: This function generates a 6-digit OTP for password reset
        Tanglish: Indha function password reset-kku 6-digit OTP generate pannum
        """
        import random
        from datetime import timedelta

        # Generate 6-digit OTP
        otp = str(random.randint(100000, 999999))

        # Set OTP and expiration (15 minutes from now)
        self.reset_otp = otp
        self.reset_otp_expires = datetime.utcnow() + timedelta(minutes=15)

        return otp

    def verify_reset_otp(self, otp):
        """
        Verify the OTP for password reset.

        Args:
            otp: OTP to verify

        Returns:
            True if OTP is valid and not expired, False otherwise

        English: This function verifies if the OTP is valid and not expired
        Tanglish: Indha function OTP valid-a irukka and expire aagala nu verify pannum
        """
        if not self.reset_otp or not self.reset_otp_expires:
            return False

        # Check if OTP matches and is not expired
        if self.reset_otp == otp and datetime.utcnow() < self.reset_otp_expires:
            return True

        return False

    def clear_reset_otp(self):
        """
        Clear the reset OTP and expiration.

        English: This function clears the reset OTP and expiration
        Tanglish: Indha function reset OTP and expiration-a clear pannum
        """
        self.reset_otp = None
        self.reset_otp_expires = None

    def update_password(self, new_password):
        """
        Update the user's password.

        Args:
            new_password: New password to set

        English: This function updates the user's password
        Tanglish: Indha function user-oda password-a update pannum
        """
        self.password = self._hash_password(new_password)

    def to_dict(self):
        """
        Convert the user to a dictionary.

        Returns:
            Dictionary representation of the user

        English: This function converts the user to a dictionary
        Tanglish: Indha function user-a dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_admin': self.is_admin,
            'course': self.course,
            'main_code': self.main_code,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
