2025-05-26 17:54:36,986 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 17:54:36,987 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:54:36,988 - werkzeug - INFO -  * Restarting with stat
2025-05-26 17:54:37,710 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 17:54:37,715 - werkzeug - INFO -  * Debugger PIN: 330-528-438
2025-05-26 17:55:52,949 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 17:55:52,952 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:55:52,954 - werkzeug - INFO -  * Restarting with stat
2025-05-26 17:55:53,639 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 17:55:53,642 - werkzeug - INFO -  * Debugger PIN: 330-528-438
2025-05-26 17:56:20,983 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 17:56:20,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,138 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-26 17:56:21,138 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,364 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-26 17:56:21,364 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,583 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-26 17:56:21,583 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:21,803 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-26 17:56:21,803 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 17:56:33,234 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:33] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-26 17:56:33,614 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:33] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:56:42,403 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:42] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:56:43,976 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:56:43] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:59:07,398 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:59:07] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-26 17:59:12,312 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:59:12] "[31m[1mGET /api/auth/login HTTP/1.1[0m" 405 -
2025-05-26 17:59:12,617 - auth_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-26 17:59:12,618 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 17:59:12] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-26 18:03:31,413 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 18:03:31,414 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 18:03:31,415 - werkzeug - INFO -  * Restarting with stat
2025-05-26 18:03:32,234 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 18:03:32,237 - werkzeug - INFO -  * Debugger PIN: 330-528-438
