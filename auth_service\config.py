"""
Configuration for the Auth Service.

This module contains configuration settings for the Auth Service.

English: This file contains settings for the Auth Service
Tanglish: Indha file Auth Service-kku settings-a contain pannum
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """
    Base configuration class.

    English: This class contains basic configuration settings
    Tanglish: Indha class basic configuration settings-a contain pannum
    """
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key')
    DEBUG = False
    TESTING = False

    # JWT settings
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET', 'your-secret-key')
    JWT_ACCESS_TOKEN_EXPIRES = 86400  # 24 hours in seconds

    # Database settings
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Email settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.hostinger.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'True').lower() == 'true'
    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'False').lower() == 'true'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME', '<EMAIL>')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD', 'IELTSGenAI@2024')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')

class DevelopmentConfig(Config):
    """
    Development configuration.

    English: This class contains settings for development environment
    Tanglish: Indha class development environment-kku settings-a contain pannum
    """
    DEBUG = True

class TestingConfig(Config):
    """
    Testing configuration.

    English: This class contains settings for testing environment
    Tanglish: Indha class testing environment-kku settings-a contain pannum
    """
    TESTING = True
    DEBUG = True

class ProductionConfig(Config):
    """
    Production configuration.

    English: This class contains settings for production environment
    Tanglish: Indha class production environment-kku settings-a contain pannum
    """
    DEBUG = False

# Dictionary of configuration environments
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}

# Get configuration based on environment
def get_config():
    """
    Get the configuration based on the environment.

    Returns:
        Configuration class

    English: This function gets the configuration based on the environment
    Tanglish: Indha function environment-a based panni configuration-a return pannum
    """
    env = os.environ.get('FLASK_ENV', 'development')
    return config_by_name[env]
